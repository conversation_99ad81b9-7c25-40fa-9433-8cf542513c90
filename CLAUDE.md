# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a React + TypeScript + Vite project for the hecara company website. It's a modern single-page application showcasing hecara's AI products and company information with a sleek, dark-themed design.

## Development Commands

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run lint` - Run ESLint linter
- `npm run preview` - Preview production build locally

## Architecture

### Tech Stack
- **Frontend Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS with custom CSS animations
- **Icons**: Lucide React
- **Linting**: ESLint 9 with TypeScript support

### Project Structure
- `src/App.tsx` - Main application component containing the entire website
- `src/main.tsx` - React application entry point
- `src/index.css` - Global styles including custom animations (starfield, geometric effects)
- `src/vite-env.d.ts` - Vite type definitions

### Key Features
- Single-page layout with smooth scrolling navigation
- Responsive design with mobile-first approach
- Custom CSS animations (starfield background, geometric elements)
- Dark theme with sophisticated styling
- Company showcase with product cards and about section

## Code Conventions

- Uses functional components with React hooks
- TypeScript strict mode enabled
- Tailwind CSS utility classes for styling
- Custom CSS for complex animations and effects
- ESLint configuration includes React hooks and refresh plugins
- Component props use TypeScript interfaces when needed

## Design System

The website uses a dark theme with:
- Primary background: black (#000000)
- Text colors: white, gray variants
- Border colors: gray-800, gray-700
- Hover effects and transitions throughout
- Custom geometric and starfield background animations
- Smooth scrolling behavior

## Important Notes

- This is a single-component application (everything in App.tsx)
- No routing library - uses smooth scrolling to sections
- Custom CSS animations are defined in index.css
- Vite optimizes dependencies, excluding 'lucide-react' from optimization
- Uses modern React patterns (StrictMode, createRoot)