import React, { useState, useEffect } from 'react';

function App() {
  const [scrolled, setScrolled] = useState(false);
  const [snakeTrail, setSnakeTrail] = useState<Array<{ x: number; y: number; timestamp: number }>>([]);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const newPosition = { x: e.clientX, y: e.clientY, timestamp: Date.now() };
      
      setSnakeTrail(prevTrail => {
        const newTrail = [newPosition, ...prevTrail];
        // Keep only the last 20 positions for longer trail
        return newTrail.slice(0, 20);
      });
    };

    // Smooth fade animation
    const fadeInterval = setInterval(() => {
      setSnakeTrail(prevTrail => {
        const now = Date.now();
        // Remove segments older than 800ms for smooth fade
        return prevTrail.filter(segment => now - segment.timestamp < 800);
      });
    }, 16); // 60fps for smooth animation

    window.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      clearInterval(fadeInterval);
    };
  }, []);

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Snake-like Mouse Follower Effect */}
      <div className="snake-follower">
        {snakeTrail.map((position, index) => {
          const now = Date.now();
          const age = now - position.timestamp;
          const maxAge = 800; // 800ms lifetime
          
          // Calculate smooth fade based on age
          const ageOpacity = Math.max(0, 1 - (age / maxAge));
          const indexOpacity = Math.max(0.1, 1 - index * 0.045); // Adjusted for 20 segments
          const finalOpacity = ageOpacity * indexOpacity;
          
          const size = 10; // Fixed 10px size for all segments
          
          return (
            <div
              key={`${position.x}-${position.y}-${position.timestamp}`}
              className="snake-segment"
              style={{
                left: position.x - size / 2,
                top: position.y - size / 2,
                width: size,
                height: size,
                opacity: finalOpacity,
                backgroundColor: '#104df0',
                zIndex: 9999 - index,
              }}
            />
          );
        })}
      </div>

      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 flex justify-center pt-4 px-4">
        <div className="transition-all duration-300 rounded-xl px-8 py-3 w-full max-w-4xl bg-black/80 backdrop-blur-sm border border-gray-700/50 shadow-[0_4px_20px_0_rgba(0,0,0,0.3)]">
          <div className="flex items-center justify-between relative">
            <h1 className="text-lg font-bold text-white">
              hecara
            </h1>
            <div className="absolute left-1/2 transform -translate-x-1/2 flex items-center space-x-6">
              <a 
                href="#projects" 
                className="text-base text-gray-300 hover:text-white transition-colors duration-200 px-3 py-1.5 rounded-lg hover:bg-white/10"
              >
                products
              </a>
              <a 
                href="#about" 
                className="text-base text-gray-300 hover:text-white transition-colors duration-200 px-3 py-1.5 rounded-lg hover:bg-white/10"
              >
                company
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="home" className="relative min-h-screen flex items-center overflow-hidden bg-black">
        {/* Grid Background */}
        <div className="absolute inset-0 hero-grid"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full relative z-10">
          <div className="max-w-4xl">
            {/* Top Label */}
            <div className="mb-8">
              <span className="text-gray-400 text-sm uppercase tracking-wider font-light">
                AI Innovation Partner
              </span>
            </div>

            {/* Main Heading */}
            <div className="space-y-2">
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-light text-white leading-tight">
                Building{' '}
                <span className="inline-flex items-center align-middle">
                  <span className="text-white px-4 py-2 text-lg font-bold rounded-sm mr-3 align-middle" style={{backgroundColor: '#1e61f3'}}>
                    hecara
                  </span>
                </span>
                AI
              </h1>
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-light text-white leading-tight">
                Products to Amplify the
              </h1>
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-light text-white leading-tight">
                Human{' '}
                <span className="text-4xl md:text-5xl lg:text-6xl mr-3">🧠</span>
                {' '}Mind.
              </h1>
            </div>
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className="py-32 bg-black relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-20">
            <div className="inline-block px-4 py-2 border border-gray-700 rounded-full mb-8">
              <span className="text-gray-400 text-sm uppercase tracking-wider">[ PRODUCTS ]</span>
            </div>
            <h2 className="text-4xl md:text-6xl font-light mb-8 text-white">
              Intelligence that enhances life
            </h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-0">
            {/* Project EchoLog */}
            <div className="bg-transparent p-8 h-full flex flex-col relative group transition-all duration-300 border-l border-r border-gray-800 lg:border-l lg:border-r hover:border-0">
              {/* Hover border effect */}
              <div className="absolute inset-0 border border-gray-600 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              {/* Corner dots for hover state */}
              <div className="absolute top-2 left-2 w-1 h-1 bg-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute top-2 right-2 w-1 h-1 bg-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute bottom-2 left-2 w-1 h-1 bg-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute bottom-2 right-2 w-1 h-1 bg-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              
              <div className="mb-8 relative z-10">
                <h3 className="text-2xl font-normal text-white mb-6">EchoLog</h3>
                <p className="text-gray-400 leading-relaxed font-light text-sm mb-8">
                  EchoLog is revolutionizing mental health through AI-powered emotional intelligence. We're building the first comprehensive platform that turns daily thoughts into actionable mental wellness insights, now accessible on web and mobile.
                </p>
              </div>
              
              <div className="flex-1 flex flex-col justify-end relative z-10">
                <div className="mb-6 h-40 overflow-hidden">
                  <img src="/echolog-icon.svg" alt="EchoLog visualization" className="w-full h-full object-cover" />
                </div>
              </div>
            </div>

            {/* Project OneWall */}
            <div className="bg-transparent p-8 h-full flex flex-col relative group transition-all duration-300 border-r border-gray-800 lg:border-r hover:border-0">
              {/* Hover border effect */}
              <div className="absolute inset-0 border border-gray-600 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              {/* Corner dots for hover state */}
              <div className="absolute top-2 left-2 w-1 h-1 bg-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute top-2 right-2 w-1 h-1 bg-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute bottom-2 left-2 w-1 h-1 bg-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute bottom-2 right-2 w-1 h-1 bg-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              
              <div className="mb-8 relative z-10">
                <h3 className="text-2xl font-normal text-white mb-6">OneWall</h3>
                <p className="text-gray-400 leading-relaxed font-light text-sm mb-8">
                  The project is currently in development phase, and no further information can be disclosed at this time.
                </p>
              </div>
              
              <div className="flex-1 flex flex-col justify-end relative z-10">
                <div className="mb-6 h-40 overflow-hidden">
                  <img src="/onewall-icon.svg" alt="OneWall stealth project" className="w-full h-full object-cover" />
                </div>
              </div>
            </div>

            {/* Project PartnerBuy */}
            <div className="bg-transparent p-8 h-full flex flex-col relative group transition-all duration-300">
              {/* Hover border effect */}
              <div className="absolute inset-0 border border-gray-600 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              {/* Corner dots for hover state */}
              <div className="absolute top-2 left-2 w-1 h-1 bg-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute top-2 right-2 w-1 h-1 bg-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute bottom-2 left-2 w-1 h-1 bg-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute bottom-2 right-2 w-1 h-1 bg-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              
              <div className="mb-8 relative z-10">
                <h3 className="text-2xl font-normal text-white mb-6">PartnerBuy</h3>
                <p className="text-gray-400 leading-relaxed font-light text-sm mb-8">
                  PartnerBuy is disrupting e-commerce with AI-native collaborative shopping experiences. We're building the next generation marketplace that leverages collective intelligence to redefine how people discover and purchase products.
                </p>
              </div>
              
              <div className="flex-1 flex flex-col justify-end relative z-10">
                <div className="mb-6 h-40 overflow-hidden">
                  <img src="/partnerbuy-icon.svg" alt="PartnerBuy commerce platform" className="w-full h-full object-cover" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-32 bg-black border-t border-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="inline-block px-4 py-2 border border-gray-700 rounded-full mb-8">
            <span className="text-gray-400 text-sm uppercase tracking-wider">[ COMPANY ]</span>
          </div>
          <h2 className="text-4xl md:text-6xl font-light mb-16 text-white">
            About hecara
          </h2>
          
          <div className="max-w-5xl">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
              <div>
                <p className="text-xl text-gray-300 leading-relaxed font-light mb-8">
                  At hecara, we're building AI that doesn't just process data—it understands the human experience. Our products are designed to enhance mental wellness, emotional intelligence, and personal growth through thoughtful technology.
                </p>
              </div>
              <div>
                <p className="text-xl text-gray-300 leading-relaxed font-light">
                  We believe the most powerful AI applications emerge when machines become partners in human flourishing. From mental health journals to commerce platforms, we're creating tools that amplify what makes us uniquely human.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black border-t border-gray-900 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center mb-4 md:mb-0">
              <h1 className="text-2xl font-bold text-white">
                hecara
              </h1>
              <span className="ml-6 text-gray-500 text-sm">© 2025 All rights reserved</span>
              <span className="ml-6 text-gray-500 text-sm">
                <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer" className="hover:text-gray-400 transition-colors">
                  鲁ICP备**********号-4
                </a>
              </span>
            </div>
            <div className="text-gray-500 text-sm font-light">
              Building AI that amplifies human intelligence
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;