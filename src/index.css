@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', '<PERSON><PERSON>s', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #000000;
}

/* Snake Effect */
.snake-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  overflow: hidden;
}

.snake-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Snake 1 */
.snake-1 {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 2px;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.6);
  animation: snake1 15s linear infinite;
}

.snake-1::before,
.snake-1::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 2px;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
}

.snake-1::before {
  top: -16px;
  opacity: 0.8;
}

.snake-1::after {
  top: -32px;
  opacity: 0.6;
}

/* Snake 2 */
.snake-2 {
  position: absolute;
  width: 6px;
  height: 6px;
  background: #1d4ed8;
  border-radius: 2px;
  box-shadow: 0 0 6px rgba(29, 78, 216, 0.6);
  animation: snake2 20s linear infinite;
}

.snake-2::before,
.snake-2::after {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  background: #1d4ed8;
  border-radius: 2px;
  box-shadow: 0 0 6px rgba(29, 78, 216, 0.4);
}

.snake-2::before {
  top: -12px;
  opacity: 0.8;
}

.snake-2::after {
  top: -24px;
  opacity: 0.6;
}

/* Snake 3 */
.snake-3 {
  position: absolute;
  width: 10px;
  height: 10px;
  background: #60a5fa;
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(96, 165, 250, 0.6);
  animation: snake3 18s linear infinite;
}

.snake-3::before,
.snake-3::after {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  background: #60a5fa;
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(96, 165, 250, 0.4);
}

.snake-3::before {
  top: -20px;
  opacity: 0.8;
}

.snake-3::after {
  top: -40px;
  opacity: 0.6;
}

/* Snake animations */
@keyframes snake1 {
  0% { transform: translate(10%, 20%) rotate(0deg); }
  25% { transform: translate(80%, 30%) rotate(90deg); }
  50% { transform: translate(70%, 80%) rotate(180deg); }
  75% { transform: translate(20%, 70%) rotate(270deg); }
  100% { transform: translate(10%, 20%) rotate(360deg); }
}

@keyframes snake2 {
  0% { transform: translate(90%, 10%) rotate(45deg); }
  25% { transform: translate(80%, 60%) rotate(135deg); }
  50% { transform: translate(30%, 90%) rotate(225deg); }
  75% { transform: translate(10%, 40%) rotate(315deg); }
  100% { transform: translate(90%, 10%) rotate(405deg); }
}

@keyframes snake3 {
  0% { transform: translate(50%, 5%) rotate(22deg); }
  25% { transform: translate(95%, 50%) rotate(112deg); }
  50% { transform: translate(60%, 95%) rotate(202deg); }
  75% { transform: translate(5%, 60%) rotate(292deg); }
  100% { transform: translate(50%, 5%) rotate(382deg); }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #000000;
}

::-webkit-scrollbar-thumb {
  background: #333333;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555555;
}

/* Snake-like Mouse Follower Effect */
.snake-follower {
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  top: 0;
  left: 0;
}

.snake-segment {
  position: absolute;
  background: #3b82f6;
  border-radius: 1px;
  transition: all 0.1s ease-out;
}

/* Multi-layer Desert Particle Background */
.wave-pattern {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    /* Large irregular sand particles */
    radial-gradient(ellipse 2px 1.5px at 15% 25%, rgba(255, 255, 255, 0.15) 1px, transparent 1.5px),
    radial-gradient(ellipse 1.5px 2px at 35% 45%, rgba(255, 250, 240, 0.12) 1px, transparent 1.5px),
    radial-gradient(ellipse 2.5px 1px at 55% 15%, rgba(255, 248, 230, 0.18) 1.2px, transparent 2px),
    radial-gradient(ellipse 1px 2.5px at 75% 65%, rgba(255, 245, 220, 0.14) 1px, transparent 1.8px),
    radial-gradient(ellipse 2px 2px at 85% 35%, rgba(255, 252, 235, 0.16) 1.3px, transparent 2px),
    radial-gradient(ellipse 1.8px 1.2px at 95% 75%, rgba(255, 255, 245, 0.13) 1px, transparent 1.5px);
  background-size: 65px 65px, 55px 55px, 75px 75px, 45px 45px, 70px 70px, 60px 60px;
  animation: desertFloat 18s linear infinite;
  opacity: 0.8;
}

.wave-pattern::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 2;
  background:
    /* Medium irregular sand particles */
    radial-gradient(ellipse 1.2px 0.8px at 18% 28%, rgba(255, 255, 255, 0.04) 0.6px, transparent 1px),
    radial-gradient(ellipse 0.9px 1.3px at 42% 68%, rgba(255, 250, 240, 0.04) 0.7px, transparent 1.1px),
    radial-gradient(ellipse 1.4px 0.7px at 62% 22%, rgba(255, 248, 230, 0.03) 0.5px, transparent 1px),
    radial-gradient(ellipse 0.8px 1.1px at 78% 42%, rgba(255, 245, 220, 0.05) 0.6px, transparent 0.9px),
    radial-gradient(ellipse 1.1px 1.4px at 88% 72%, rgba(255, 252, 235, 0.04) 0.7px, transparent 1.2px),
    radial-gradient(ellipse 1.3px 0.9px at 12% 78%, rgba(255, 255, 245, 0.04) 0.8px, transparent 1.1px),
    radial-gradient(ellipse 0.7px 1.2px at 32% 12%, rgba(255, 250, 240, 0.03) 0.5px, transparent 0.8px),
    radial-gradient(ellipse 1.5px 1px at 52% 52%, rgba(255, 248, 225, 0.05) 0.9px, transparent 1.3px);
  background-size: 45px 45px, 38px 38px, 52px 52px, 35px 35px, 48px 48px, 42px 42px, 40px 40px, 50px 50px;
  animation: desertFloat2 25s linear infinite reverse;
  transform: translateZ(10px) scale(1.02);
  filter: drop-shadow(0 0.5px 1px rgba(255, 250, 240, 0.08)) drop-shadow(0 1px 2px rgba(255, 248, 230, 0.04));
}

.wave-pattern::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 3;
  background:
    /* Micro sand dust particles */
    radial-gradient(ellipse 0.4px 0.3px at 7% 17%, rgba(255, 255, 255, 0.02) 0.2px, transparent 0.3px),
    radial-gradient(ellipse 0.3px 0.5px at 27% 47%, rgba(255, 250, 240, 0.02) 0.25px, transparent 0.4px),
    radial-gradient(ellipse 0.5px 0.2px at 57% 27%, rgba(255, 248, 230, 0.015) 0.15px, transparent 0.35px),
    radial-gradient(ellipse 0.2px 0.4px at 77% 67%, rgba(255, 245, 220, 0.025) 0.3px, transparent 0.4px),
    radial-gradient(ellipse 0.4px 0.4px at 87% 37%, rgba(255, 252, 235, 0.02) 0.2px, transparent 0.3px),
    radial-gradient(ellipse 0.3px 0.3px at 97% 87%, rgba(255, 255, 245, 0.02) 0.25px, transparent 0.35px),
    radial-gradient(ellipse 0.6px 0.3px at 17% 7%, rgba(255, 250, 240, 0.015) 0.2px, transparent 0.4px),
    radial-gradient(ellipse 0.2px 0.6px at 37% 77%, rgba(255, 248, 225, 0.03) 0.35px, transparent 0.5px),
    radial-gradient(ellipse 0.5px 0.5px at 67% 47%, rgba(255, 250, 235, 0.02) 0.25px, transparent 0.4px),
    radial-gradient(ellipse 0.3px 0.4px at 47% 17%, rgba(255, 252, 240, 0.02) 0.2px, transparent 0.3px);
  background-size: 25px 25px, 22px 22px, 28px 28px, 20px 20px, 26px 26px, 24px 24px,
                   23px 23px, 27px 27px, 29px 29px, 21px 21px;
  animation: desertFloat3 22s linear infinite;
  transform: translateZ(20px) scale(1.05);
  filter: drop-shadow(0 0.25px 0.5px rgba(255, 255, 255, 0.06)) drop-shadow(0 0.5px 1px rgba(255, 250, 240, 0.03));
}

/* Desert wind-like animations */
@keyframes desertFloat {
  0% {
    background-position: 0px 0px, 15px -8px, -12px 6px, 8px -15px, -6px 12px, 18px -4px;
  }
  25% {
    background-position: 45px 15px, 55px 8px, 35px 25px, 25px -5px, 15px 20px, 40px 10px;
  }
  50% {
    background-position: 120px 45px, 140px 35px, 100px 55px, 80px 25px, 60px 50px, 110px 40px;
  }
  75% {
    background-position: 180px 25px, 200px 15px, 160px 35px, 140px 5px, 120px 30px, 170px 20px;
  }
  100% {
    background-position: 250px 60px, 270px 50px, 230px 70px, 210px 40px, 190px 65px, 240px 55px;
  }
}

@keyframes desertFloat2 {
  0% {
    background-position: 0px 0px, -4px 8px, 8px -4px, -8px 12px, 4px -8px, 12px 4px, 6px -6px, -6px 10px;
  }
  30% {
    background-position: 25px 8px, 20px 15px, 30px 5px, 15px 18px, 22px 2px, 28px 12px, 24px 6px, 18px 16px;
  }
  60% {
    background-position: 55px 20px, 50px 28px, 60px 15px, 45px 32px, 52px 12px, 58px 25px, 54px 18px, 48px 30px;
  }
  100% {
    background-position: 90px 35px, 85px 45px, 95px 30px, 80px 50px, 87px 25px, 93px 40px, 89px 32px, 83px 48px;
  }
}

@keyframes desertFloat3 {
  0% {
    background-position: 0px 0px, 3px -3px, -3px 2px, 6px -6px, -6px 4px, 2px -2px,
                         4px -4px, -4px 3px, 5px -5px, -2px 1px;
  }
  40% {
    background-position: 15px 5px, 18px 2px, 12px 7px, 21px -1px, 9px 9px, 17px 3px,
                         19px 1px, 11px 8px, 20px 0px, 13px 6px;
  }
  100% {
    background-position: 35px 12px, 38px 8px, 32px 15px, 41px 5px, 29px 18px, 37px 10px,
                         39px 7px, 31px 16px, 40px 6px, 33px 14px;
  }
}

/* Hero Grid Background */
.hero-grid {
  background-image:
    linear-gradient(to right, rgba(64, 64, 64, 0.3) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(64, 64, 64, 0.3) 1px, transparent 1px);
  background-size: 40px 40px;
  background-position: 0 0;
  mask: radial-gradient(ellipse 80% 60% at 85% 15%, black 0%, black 30%, transparent 70%);
  -webkit-mask: radial-gradient(ellipse 80% 60% at 85% 15%, black 0%, black 30%, transparent 70%);
  opacity: 0.6;
}

/* Smooth transitions */
* {
  transition: color 0.2s ease, border-color 0.2s ease, background-color 0.2s ease;
}